name: Deploy to InfinityFree via FTP

on:
  push:
    branches:
      - main

jobs:
  ftp-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout del código
        uses: actions/checkout@v3

      - name: <PERSON><PERSON><PERSON> por FTP
        uses: SamKirkland/FTP-Deploy-Action@v4.3.4
        with:
          server: ${{ secrets.FTP_SERVER }}
          username: ${{ secrets.FTP_USERNAME }}
          password: ${{ secrets.FTP_PASSWORD }}
          server-dir: /clashstrategic.great-site.net/htdocs/webapp
          local-dir: ./ # Raíz del proyecto
          exclude: |
            .github/
            node_modules/
            tests/
            .env
            update-sw-version.js
            .releaserc
            .gitignore
            eslint.config.mjs
            package-lock.json
            package.json
